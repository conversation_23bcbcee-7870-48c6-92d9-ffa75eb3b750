import numpy as np

from gym_art.quadrotor_multi.scenarios.utils import get_z_value
from gym_art.quadrotor_multi.scenarios.base import QuadrotorScenario


class Scenario_wall_jump(QuadrotorScenario):
    def __init__(self, quads_mode, envs, num_agents, room_dims):
        super().__init__(quads_mode, envs, num_agents, room_dims)
        # Wall spawns every [4.0, 6.0] secs
        duration_time = 5.0
        self.control_step_for_sec = int(duration_time * self.envs[0].control_freq)
        
        # Wall properties
        self.wall_position = None
        self.wall_height = 2.0  # Default wall height
        self.wall_width = None  # Will be set to full room width
        self.wall_thickness = 0.3  # Wall thickness
        
        # Wall height range (meters)
        self.min_wall_height = 1.0
        self.max_wall_height = 4.0
        
        # Goal position (behind the wall)
        self.goal_distance_behind_wall = 4.0  # Further behind wall
        self.drone_distance_front_wall = 4.0  # Distance in front of wall

        # Note: Wall and goal will be initialized in reset() method

    def spawn_new_wall_and_goal(self):
        """Spawn a new wall with random height and set goal behind it"""
        # Make sure formation is initialized
        if self.formation is None:
            self.update_formation_and_relate_param()

        # Get actual room dimensions (not spawn box)
        room_length = self.envs[0].room_length  # X direction
        room_width = self.envs[0].room_width    # Y direction
        room_height = self.envs[0].room_height  # Z direction

        # Random wall height
        self.wall_height = np.random.uniform(self.min_wall_height, self.max_wall_height)

        # Wall spans ENTIRE room width in X direction (no side passages!)
        self.wall_width = room_length  # Full room width in X

        # Wall position: Place wall in the middle of the room (Y direction)
        wall_x = 0.0  # Center of room in X direction
        wall_y = 0.0  # Center of room in Y direction
        wall_z = self.wall_height / 2.0  # Wall center height

        self.wall_position = np.array([wall_x, wall_y, wall_z])

        # Set drone spawn area (in front of wall, negative Y)
        drone_spawn_y = wall_y - self.drone_distance_front_wall
        drone_spawn_y = max(drone_spawn_y, -room_width/2 + 1.0)  # Ensure within room bounds

        # Set goal area (behind wall, positive Y) - MUCH further behind
        goal_y = wall_y + self.goal_distance_behind_wall
        goal_y = min(goal_y, room_width/2 - 1.0)  # Ensure within room bounds

        # Calculate formation center for drones (in front of wall)
        goal_z = get_z_value(num_agents=self.num_agents,
                           num_agents_per_layer=self.num_agents_per_layer,
                           box_size=2.0,  # Use standard spawn box size
                           formation=self.formation,
                           formation_size=self.formation_size)

        # Set drone formation center (where drones spawn) - in front of wall
        self.formation_center = np.array([0.0, drone_spawn_y, goal_z])

        # Set goal formation center (where goals are placed) - behind wall
        self.goal_formation_center = np.array([0.0, goal_y, goal_z])



        # Update wall in all environments
        for env in self.envs:
            if hasattr(env, 'set_wall'):
                env.set_wall(self.wall_position, self.wall_height, self.wall_width, self.wall_thickness)

    def update_goals(self):
        """Update goals behind the current wall"""
        # Generate goals behind the wall using goal_formation_center
        # Note: Don't call update_formation_and_relate_param() here as it would reset formation parameters
        self.goals = self.generate_goals(num_agents=self.num_agents,
                                       formation_center=self.goal_formation_center,
                                       layer_dist=self.layer_dist)
        np.random.shuffle(self.goals)

    def step(self):
        tick = self.envs[0].tick
        if tick % self.control_step_for_sec == 0 and tick > 0:
            # Spawn new wall and goal
            self.spawn_new_wall_and_goal()
            self.update_goals()

            # Update goals to envs
            for i, env in enumerate(self.envs):
                env.goal = self.goals[i]

        return

    def reset(self):
        # Update duration time
        duration_time = np.random.uniform(low=4.0, high=6.0)
        self.control_step_for_sec = int(duration_time * self.envs[0].control_freq)

        # Reset formation and related parameters (but don't create goals yet)
        self.update_formation_and_relate_param()

        # Spawn initial wall and goal (after formation is set)
        self.spawn_new_wall_and_goal()

        # Generate drone starting positions (in front of wall)
        self.drone_positions = self.generate_goals(num_agents=self.num_agents,
                                                 formation_center=self.formation_center,
                                                 layer_dist=self.layer_dist)

        # Set drone starting positions
        for i, env in enumerate(self.envs):
            env.dynamics.pos = self.drone_positions[i].copy()
            env.dynamics.vel = np.zeros(3)  # Start with zero velocity
            env.dynamics.omega = np.zeros(3)  # Start with zero angular velocity

        # Generate goals behind the wall using goal_formation_center
        self.goals = self.generate_goals(num_agents=self.num_agents,
                                       formation_center=self.goal_formation_center,
                                       layer_dist=self.layer_dist)
        np.random.shuffle(self.goals)

        # Update goals to envs
        for i, env in enumerate(self.envs):
            env.goal = self.goals[i]

    def get_wall_info(self):
        """Get current wall information for visualization"""
        if self.wall_position is not None:
            return {
                'position': self.wall_position.tolist(),
                'height': float(self.wall_height),
                'width': float(self.wall_width),
                'thickness': float(self.wall_thickness)
            }
        return None
