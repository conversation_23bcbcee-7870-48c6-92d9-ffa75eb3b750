#!/usr/bin/env python3

import numpy as np
import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gym_art.quadrotor_multi.quadrotor_multi import QuadrotorEnv<PERSON>ult<PERSON>

def test_wall_jump_scenario():
    """Test wall_jump scenario to debug goal positioning"""

    # Create environment with wall_jump scenario
    print("Creating QuadrotorEnvMulti environment with wall_jump scenario...")
    env = QuadrotorEnvMulti(
        num_agents=4,
        ep_time=10.0,
        rew_coeff=[1.0, 1.0, 1.0, 1.0, 1.0],
        obs_repr='xyz_vxyz_R_omega',
        neighbor_visible_num=8,
        neighbor_obs_type='pos_vel',
        collision_hitbox_radius=0.35,
        collision_falloff_radius=0.7,
        use_obstacles=False,
        obst_density=0.0,
        obst_size=0.0,
        obst_spawn_area=0.0,
        use_downwash=False,
        use_numba=False,
        quads_mode='wall_jump',
        room_dims=[10.0, 10.0, 10.0],
        use_replay_buffer=False,
        quads_view_mode=['chase'],
        quads_render=False,
        dynamics_params='default',
        raw_control=False,
        raw_control_zero_middle=True,
        dynamics_randomize_every=0,
        dynamics_change=0.0,
        dyn_sampler_1=None,
        sense_noise='none',
        init_random_state=False,
        render_mode='human'
    )
    
    print("\nResetting environment...")
    obs = env.reset()
    
    print(f"\nEnvironment reset complete. Observation shape: {obs.shape}")
    
    # Get wall info from scenario
    if hasattr(env.scenario, 'get_wall_info'):
        wall_info = env.scenario.get_wall_info()
        if wall_info:
            print(f"\nWall Info:")
            print(f"  Position: {wall_info['position']}")
            print(f"  Height: {wall_info['height']}")
            print(f"  Width: {wall_info['width']}")
            print(f"  Thickness: {wall_info['thickness']}")
    
    # Print current goals
    print(f"\nCurrent goals in environments:")
    for i, env_instance in enumerate(env.envs):
        goal = env_instance.goal
        print(f"  Env {i} goal: X={goal[0]:.2f}, Y={goal[1]:.2f}, Z={goal[2]:.2f}")
    
    # Print drone positions
    print(f"\nCurrent drone positions:")
    for i, env_instance in enumerate(env.envs):
        pos = env_instance.dynamics.pos
        print(f"  Drone {i} pos: X={pos[0]:.2f}, Y={pos[1]:.2f}, Z={pos[2]:.2f}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    test_wall_jump_scenario()
