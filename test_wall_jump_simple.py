#!/usr/bin/env python3

import numpy as np
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_wall_jump_scenario_directly():
    """Test wall_jump scenario directly without full environment"""

    try:
        from gym_art.quadrotor_multi.scenarios.wall_jump import Scenario_wall_jump

        # Create mock environment objects
        class MockEnv:
            def __init__(self):
                self.room_length = 10.0
                self.room_width = 10.0
                self.room_height = 10.0
                self.control_freq = 50.0
                self.goal = np.array([0.0, 0.0, 2.0])

            def set_wall(self, position, height, width, thickness):
                print(f"Mock: Setting wall at {position} with height={height}, width={width}, thickness={thickness}")

        # Create mock environments
        envs = [MockEnv() for _ in range(4)]

        print("Creating wall_jump scenario...")
        scenario = Scenario_wall_jump(
            quads_mode='wall_jump',
            envs=envs,
            num_agents=4,
            room_dims=[10.0, 10.0, 10.0]
        )

        print("Calling scenario reset...")
        scenario.reset()

        print("Test completed successfully!")

        # Print wall info
        wall_info = scenario.get_wall_info()
        if wall_info:
            print(f"\nWall Info:")
            print(f"  Position: {wall_info['position']}")
            print(f"  Height: {wall_info['height']}")
            print(f"  Width: {wall_info['width']}")
            print(f"  Thickness: {wall_info['thickness']}")

        # Print goals
        if hasattr(scenario, 'goals') and scenario.goals is not None:
            print(f"\nGoals:")
            for i, goal in enumerate(scenario.goals):
                print(f"  Goal {i}: X={goal[0]:.2f}, Y={goal[1]:.2f}, Z={goal[2]:.2f}")

        return True

    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_wall_jump_scenario_directly()
    sys.exit(0 if success else 1)
